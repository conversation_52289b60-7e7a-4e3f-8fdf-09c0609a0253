# Task 04: Database Connection Service for Permissions

## Objective
Create a specialized database service for RBAC permission operations. This service will handle all database interactions for roles, permissions, and user access control, following ProManage's centralized database architecture.

## Priority
**FOUNDATION** - Depends on Tasks 01-03

## Estimated Time
1.5 hours

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup

## Files to Create
- `Modules/Connections/PermissionDatabaseService.cs`
- `Modules/Procedures/System/Permission-Queries.sql`

## Database Service Implementation

### PermissionDatabaseService.cs
```csharp
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using ProManage.Modules.Models;

namespace ProManage.Modules.Connections
{
    public class PermissionDatabaseService
    {
        private readonly DatabaseConnectionManager _connectionManager;
        private readonly DatabaseQueryExecutor _queryExecutor;
        
        public PermissionDatabaseService()
        {
            _connectionManager = new DatabaseConnectionManager();
            _queryExecutor = new DatabaseQueryExecutor();
        }
        
        #region Role Operations
        
        /// <summary>
        /// Get all active roles
        /// </summary>
        public List<Role> GetAllRoles()
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, modified_date 
                FROM roles 
                WHERE is_active = 1 
                ORDER BY role_name";
            
            var roles = new List<Role>();
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            roles.Add(new Role
                            {
                                RoleId = reader.GetInt32("role_id"),
                                RoleName = reader.GetString("role_name"),
                                Description = reader.IsDBNull("description") ? null : reader.GetString("description"),
                                IsActive = reader.GetBoolean("is_active"),
                                CreatedDate = reader.GetDateTime("created_date"),
                                ModifiedDate = reader.GetDateTime("modified_date")
                            });
                        }
                    }
                }
            }
            
            return roles;
        }
        
        /// <summary>
        /// Get role by ID
        /// </summary>
        public Role GetRoleById(int roleId)
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, modified_date 
                FROM roles 
                WHERE role_id = @roleId";
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@roleId", roleId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Role
                            {
                                RoleId = reader.GetInt32("role_id"),
                                RoleName = reader.GetString("role_name"),
                                Description = reader.IsDBNull("description") ? null : reader.GetString("description"),
                                IsActive = reader.GetBoolean("is_active"),
                                CreatedDate = reader.GetDateTime("created_date"),
                                ModifiedDate = reader.GetDateTime("modified_date")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        #endregion
        
        #region Role Permission Operations
        
        /// <summary>
        /// Get role permissions for specific role
        /// </summary>
        public List<RolePermission> GetRolePermissions(int roleId)
        {
            const string query = @"
                SELECT permission_id, role_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions 
                WHERE role_id = @roleId 
                ORDER BY form_name";
            
            var permissions = new List<RolePermission>();
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@roleId", roleId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            permissions.Add(new RolePermission
                            {
                                PermissionId = reader.GetInt32("permission_id"),
                                RoleId = reader.GetInt32("role_id"),
                                FormName = reader.GetString("form_name"),
                                ReadPermission = reader.GetBoolean("read_permission"),
                                NewPermission = reader.GetBoolean("new_permission"),
                                EditPermission = reader.GetBoolean("edit_permission"),
                                DeletePermission = reader.GetBoolean("delete_permission"),
                                PrintPermission = reader.GetBoolean("print_permission"),
                                CreatedDate = reader.GetDateTime("created_date")
                            });
                        }
                    }
                }
            }
            
            return permissions;
        }
        
        /// <summary>
        /// Get specific role permission
        /// </summary>
        public RolePermission GetRolePermission(int roleId, string formName)
        {
            const string query = @"
                SELECT permission_id, role_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions 
                WHERE role_id = @roleId AND form_name = @formName";
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@roleId", roleId);
                    command.Parameters.AddWithValue("@formName", formName);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new RolePermission
                            {
                                PermissionId = reader.GetInt32("permission_id"),
                                RoleId = reader.GetInt32("role_id"),
                                FormName = reader.GetString("form_name"),
                                ReadPermission = reader.GetBoolean("read_permission"),
                                NewPermission = reader.GetBoolean("new_permission"),
                                EditPermission = reader.GetBoolean("edit_permission"),
                                DeletePermission = reader.GetBoolean("delete_permission"),
                                PrintPermission = reader.GetBoolean("print_permission"),
                                CreatedDate = reader.GetDateTime("created_date")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Update role permissions
        /// </summary>
        public bool UpdateRolePermissions(List<RolePermissionUpdate> updates)
        {
            using (var connection = _connectionManager.GetConnection())
            {
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        foreach (var update in updates)
                        {
                            const string query = @"
                                UPDATE role_permissions 
                                SET read_permission = @readPermission,
                                    new_permission = @newPermission,
                                    edit_permission = @editPermission,
                                    delete_permission = @deletePermission,
                                    print_permission = @printPermission
                                WHERE role_id = @roleId AND form_name = @formName";
                            
                            using (var command = new SqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@roleId", update.RoleId);
                                command.Parameters.AddWithValue("@formName", update.FormName);
                                command.Parameters.AddWithValue("@readPermission", update.ReadPermission);
                                command.Parameters.AddWithValue("@newPermission", update.NewPermission);
                                command.Parameters.AddWithValue("@editPermission", update.EditPermission);
                                command.Parameters.AddWithValue("@deletePermission", update.DeletePermission);
                                command.Parameters.AddWithValue("@printPermission", update.PrintPermission);
                                
                                command.ExecuteNonQuery();
                            }
                        }
                        
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        return false;
                    }
                }
            }
        }
        
        #endregion
        
        #region User Permission Operations
        
        /// <summary>
        /// Get user permissions (overrides only)
        /// </summary>
        public List<UserPermission> GetUserPermissions(int userId)
        {
            const string query = @"
                SELECT user_permission_id, user_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions 
                WHERE user_id = @userId 
                ORDER BY form_name";
            
            var permissions = new List<UserPermission>();
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@userId", userId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            permissions.Add(new UserPermission
                            {
                                UserPermissionId = reader.GetInt32("user_permission_id"),
                                UserId = reader.GetInt32("user_id"),
                                FormName = reader.GetString("form_name"),
                                ReadPermission = reader.IsDBNull("read_permission") ? (bool?)null : reader.GetBoolean("read_permission"),
                                NewPermission = reader.IsDBNull("new_permission") ? (bool?)null : reader.GetBoolean("new_permission"),
                                EditPermission = reader.IsDBNull("edit_permission") ? (bool?)null : reader.GetBoolean("edit_permission"),
                                DeletePermission = reader.IsDBNull("delete_permission") ? (bool?)null : reader.GetBoolean("delete_permission"),
                                PrintPermission = reader.IsDBNull("print_permission") ? (bool?)null : reader.GetBoolean("print_permission"),
                                CreatedDate = reader.GetDateTime("created_date")
                            });
                        }
                    }
                }
            }
            
            return permissions;
        }
        
        /// <summary>
        /// Get specific user permission
        /// </summary>
        public UserPermission GetUserPermission(int userId, string formName)
        {
            const string query = @"
                SELECT user_permission_id, user_id, form_name, read_permission, new_permission, 
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions 
                WHERE user_id = @userId AND form_name = @formName";
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@userId", userId);
                    command.Parameters.AddWithValue("@formName", formName);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new UserPermission
                            {
                                UserPermissionId = reader.GetInt32("user_permission_id"),
                                UserId = reader.GetInt32("user_id"),
                                FormName = reader.GetString("form_name"),
                                ReadPermission = reader.IsDBNull("read_permission") ? (bool?)null : reader.GetBoolean("read_permission"),
                                NewPermission = reader.IsDBNull("new_permission") ? (bool?)null : reader.GetBoolean("new_permission"),
                                EditPermission = reader.IsDBNull("edit_permission") ? (bool?)null : reader.GetBoolean("edit_permission"),
                                DeletePermission = reader.IsDBNull("delete_permission") ? (bool?)null : reader.GetBoolean("delete_permission"),
                                PrintPermission = reader.IsDBNull("print_permission") ? (bool?)null : reader.GetBoolean("print_permission"),
                                CreatedDate = reader.GetDateTime("created_date")
                            });
                        }
                    }
                }
            }
            
            return null;
        }
        
        #endregion
        
        #region Global Permission Operations
        
        /// <summary>
        /// Get global permissions for user
        /// </summary>
        public GlobalPermission GetGlobalPermissions(int userId)
        {
            const string query = @"
                SELECT global_permission_id, user_id, can_create_users, can_edit_users, 
                       can_delete_users, can_print_users, created_date
                FROM global_permissions 
                WHERE user_id = @userId";
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@userId", userId);
                    
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new GlobalPermission
                            {
                                GlobalPermissionId = reader.GetInt32("global_permission_id"),
                                UserId = reader.GetInt32("user_id"),
                                CanCreateUsers = reader.GetBoolean("can_create_users"),
                                CanEditUsers = reader.GetBoolean("can_edit_users"),
                                CanDeleteUsers = reader.GetBoolean("can_delete_users"),
                                CanPrintUsers = reader.GetBoolean("can_print_users"),
                                CreatedDate = reader.GetDateTime("created_date")
                            };
                        }
                    }
                }
            }
            
            return null;
        }
        
        #endregion
        
        #region Form Management
        
        /// <summary>
        /// Add form permissions for all roles
        /// </summary>
        public bool AddFormToPermissionSystem(string formName)
        {
            const string query = @"
                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                SELECT role_id, @formName, 0, 0, 0, 0, 0
                FROM roles
                WHERE NOT EXISTS (
                    SELECT 1 FROM role_permissions
                    WHERE role_id = roles.role_id AND form_name = @formName
                )";
            
            using (var connection = _connectionManager.GetConnection())
            {
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@formName", formName);
                    return command.ExecuteNonQuery() > 0;
                }
            }
        }
        
        /// <summary>
        /// Remove form from permission system
        /// </summary>
        public bool RemoveFormFromPermissionSystem(string formName)
        {
            using (var connection = _connectionManager.GetConnection())
            {
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Remove user permissions first
                        const string deleteUserPerms = "DELETE FROM user_permissions WHERE form_name = @formName";
                        using (var command = new SqlCommand(deleteUserPerms, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@formName", formName);
                            command.ExecuteNonQuery();
                        }
                        
                        // Remove role permissions
                        const string deleteRolePerms = "DELETE FROM role_permissions WHERE form_name = @formName";
                        using (var command = new SqlCommand(deleteRolePerms, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@formName", formName);
                            command.ExecuteNonQuery();
                        }
                        
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        return false;
                    }
                }
            }
        }
        
        #endregion
    }
}
```

## Implementation Guidelines

### Database Connection Pattern
- Use existing DatabaseConnectionManager for connections
- Follow ProManage's transaction handling patterns
- Implement proper error handling and resource disposal
- Use parameterized queries to prevent SQL injection

### Performance Considerations
- Implement connection pooling through existing manager
- Use appropriate indexes on permission tables
- Cache frequently accessed permissions
- Batch operations where possible

### Error Handling
- Return null for not found items
- Return false for failed operations
- Log errors without exposing sensitive information
- Graceful degradation for permission checks

## Acceptance Criteria

- [ ] All CRUD operations for roles, permissions implemented
- [ ] Proper transaction handling for multi-table operations
- [ ] Parameterized queries for security
- [ ] Integration with existing DatabaseConnectionManager
- [ ] Null handling for optional permission values
- [ ] Form addition/removal operations
- [ ] Global permission operations
- [ ] Proper resource disposal (using statements)

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup

## Next Tasks
This task enables:
- Task 05: Form Discovery Service Implementation
- Task 06: Core Permission Service Logic
- Task 07: Permission Database Operations
